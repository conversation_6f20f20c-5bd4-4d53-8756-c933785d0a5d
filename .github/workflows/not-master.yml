name: Rust Build

on:
  push:
    branches-ignore:
      - master
      - production

jobs:
  build:
    runs-on: ubuntu-24.04

    steps:
      - name: Check out code
        uses: actions/checkout@v4

      - uses: inclusive-finance/rust-cache@master
        with:
          # Determines whether workspace `target` directories are cached.
          # If `false`, only the cargo registry will be cached.
          cache-targets: "true"
          cache-on-failure: "true"
          save-if: ${{ github.ref == 'refs/heads/master' || github.ref == 'refs/heads/production' }}

      - name: Build the application
        run: cargo build --release

      - name: Run tests
        run: cargo test --release
        env:
          RUST_LOG: info
          ONEINCH_API_KEY: ${{ secrets.ONEINCH_API_KEY }}