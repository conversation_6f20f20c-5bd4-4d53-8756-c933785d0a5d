name: Build, Push and Deploy

on:
  push:
    branches:
      - production

jobs:
  build-push-and-deploy:
    runs-on: ubuntu-24.04
    permissions:
      id-token: write
      packages: write
      contents: read
    steps:
      - name: Checkout repository
        uses: actions/checkout@v4

      - uses: inclusive-finance/rust-cache@master
        with:
          # Determines whether workspace `target` directories are cached.
          # If `false`, only the cargo registry will be cached.
          cache-targets: "true"
          cache-on-failure: "true"
          save-if: ${{ github.ref == 'refs/heads/master' || github.ref == 'refs/heads/production' }}

      - name: Build the application
        run: cargo build --release

      - name: Run tests
        run: cargo test --release
        env:
          RUST_LOG: info
          ONEINCH_API_KEY: ${{ secrets.ONEINCH_API_KEY }}

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3

      - name: Log in to Docker Registry
        uses: docker/login-action@v3
        with:
          registry: ghcr.io
          username: ${{ github.actor }}
          password: ${{ secrets.GITHUB_TOKEN }}

      - name: Set Image Name
        run: echo IMAGE_REPOSITORY=$(echo "${{ github.repository }}" | tr '[:upper:]' '[:lower:]') >> $GITHUB_ENV

      - name: Push Image to Github Container Registry
        uses: docker/build-push-action@v6
        with:
          context: .
          push: true
          tags: ghcr.io/${{ env.IMAGE_REPOSITORY }}:latest

      - name: Configure AWS Credentials
        uses: aws-actions/configure-aws-credentials@v3
        with:
          aws-region: us-east-1
          role-to-assume: ${{ secrets.AWS_IAM_ROLE_ARN }}

      - name: Deploy new ECS Task
        run: aws ecs update-service --cluster production-cluster --service il-price-feed --task-definition production-il-price-feed --force-new-deployment