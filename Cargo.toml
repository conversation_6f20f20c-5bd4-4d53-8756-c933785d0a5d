[package]
name = "price-feed"
version = "0.1.0"
edition = "2021"

[dependencies]
reqwest = { version = "0.12.9", features = ["json"] }
tokio = { version = "1.39.0", features = ["full"] }
serde = { version = "1.0.200", features = ["derive"] }
thiserror = "2.0.7"
serde_json = "1.0.133"
log = "0.4.22"
env_logger = "0.11.5"
futures-util = "0.3.31"
tokio-tungstenite = "0.26.0"
time = { version = "0.3", features = ["serde"] }
async-trait = "0.1"
actix-web = "4.3"
firestore = "0.44.0"
alloy = { version = "0.9", features = ["full"] }
solana-client = "2.1.10"
solana-sdk = "2.1.10"
solana-transaction-status = "2.1.10"
spl-token = "6.0.0"
anyhow = "1.0.97"
tokio-postgres = { version = "0.7.12", features = ["with-serde_json-1"] }
openssl = { version = "0.10", features = ["vendored"] }
postgres-openssl = "0.5.1"
chrono = "0.4.41"
rust_decimal = { version = "1.37.1", features = ["db-postgres"] }
actix-cors = "0.7.1"

[dev-dependencies]
async-trait = "0.1.83"
dotenv = "0.15.0"
