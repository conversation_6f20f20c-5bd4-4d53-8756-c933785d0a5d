use futures_util::{stream::StreamExt, SinkExt};
use price_feed::{feed::Feed, handle_connection, ChannelPublisher, structs::QuoteRequest, structs::QuoteResponse, SharedFeeds, feed_publisher::FeedPublisher};
use time::OffsetDateTime;
use std::{collections::HashMap, sync::{Arc, Mutex}, time::Duration};
use tokio::{net::TcpListener, sync::broadcast};
use tokio_tungstenite::connect_async;



#[tokio::test]
async fn test_quote_request_handling() {
    // Set up a test server
    let listener = TcpListener::bind("127.0.0.1:0").await.unwrap();
    let addr = listener.local_addr().unwrap();

    // Create a broadcast channel
    let (tx, _) = broadcast::channel::<Feed>(100);
    let tx = Arc::new(tx);
    let tx_clone = tx.clone();

    // Spawn the server
    tokio::spawn(async move {
        let (stream, _) = listener.accept().await.unwrap();
        handle_connection(stream, tx_clone).await;
    });

    // Connect a test client
    let (ws_stream, _) = connect_async(format!("ws://{}", addr))
        .await
        .expect("Failed to connect");
    let (mut write, mut read) = ws_stream.split();

    // Create and send a quote request first
    let quote_request = QuoteRequest {
        from_chain: "ethereum".to_string(),
        to_chain: "ethereum".to_string(),
        from_token: "******************************************".to_string(), // WETH
        to_token: "******************************************".to_string(),   // USDC
        from_amount: "1.0".to_string(),
        from_address: "0x1234...".to_string(),
        slippage: None,
    };

    let request_msg = tokio_tungstenite::tungstenite::Message::Text(
        serde_json::to_string(&quote_request).unwrap().into(),
    );
    write
        .send(request_msg)
        .await
        .expect("Failed to send request");

    // Create and send the feed update
    let mut prices = HashMap::new();
    prices.insert(
        "******************************************".to_string(), // WETH
        2000.0,
    );
    prices.insert(
        "******************************************".to_string(), // USDC
        1.0,
    );

    let feed = Feed {
        prices,
        source: "test".to_string(),
        timestamp: OffsetDateTime::now_utc().unix_timestamp() as u64,
        supported_chains: vec!["ethereum".into()].into_iter().collect(),
    };

    tx.send(feed).expect("Failed to send feed");

    // Read messages until we get the quote response
    while let Some(Ok(msg)) = read.next().await {
        if let tokio_tungstenite::tungstenite::Message::Text(response_str) = msg {
            // Try to parse as QuoteResponse
            if let Ok(response) = serde_json::from_str::<QuoteResponse>(&response_str) {
                // Verify the response
                assert_eq!(response.from_token, quote_request.from_token);
                assert_eq!(response.to_token, quote_request.to_token);
                assert_eq!(response.from_amount, quote_request.from_amount);

                // Check if to_amount is correct (1 WETH = 2000 USDC)
                if let Some(to_amount) = response.to_amount {
                    let amount: f64 = to_amount.parse().unwrap();
                    assert!((amount - 2000.0).abs() < 0.001); // Allow for small floating-point differences
                } else {
                    panic!("Expected to_amount to be Some");
                }
                return; // Test passed
            }
            // If it's not a QuoteResponse, it might be a Feed update, just continue
        }
    }
    panic!("Never received quote response");
}

#[tokio::test]
async fn test_multiple_feed_sources() {
    // Set up a test server
    let listener = TcpListener::bind("127.0.0.1:0").await.unwrap();
    let addr = listener.local_addr().unwrap();

    // Create a broadcast channel
    let (tx, _) = broadcast::channel::<Feed>(100);
    let tx = Arc::new(tx);
    let tx_clone = tx.clone();

    // Spawn the server
    tokio::spawn(async move {
        let (stream, _) = listener.accept().await.unwrap();
        handle_connection(stream, tx_clone).await;
    });

    // Connect a test client
    let (ws_stream, _) = connect_async(format!("ws://{}", addr))
        .await
        .expect("Failed to connect");
    let (mut write, mut read) = ws_stream.split();

    // Create and send a quote request first
    let quote_request = QuoteRequest {
        from_chain: "ethereum".to_string(),
        to_chain: "ethereum".to_string(),
        from_token: "******************************************".to_string(), // WETH
        to_token: "******************************************".to_string(),   // USDC
        from_amount: "1.0".to_string(),
        from_address: "0x1234...".to_string(),
        slippage: None,
    };

    let request_msg = tokio_tungstenite::tungstenite::Message::Text(
        serde_json::to_string(&quote_request).unwrap().into(),
    );
    write
        .send(request_msg)
        .await
        .expect("Failed to send request");

    tokio::time::sleep(Duration::from_millis(100)).await;

    // Create and send the feed update
    let mut prices = HashMap::new();
    prices.insert(
        "******************************************".to_string(), // WETH
        2000.0,
    );
    prices.insert(
        "******************************************".to_string(), // USDC
        1.0,
    );

    let feed1 = Feed {
        prices,
        source: "1inch".to_string(),
        timestamp: OffsetDateTime::now_utc().unix_timestamp() as u64,
        supported_chains: vec!["ethereum".into()].into_iter().collect(),
    };

    tx.send(feed1).expect("Failed to send feed");

    tokio::time::sleep(Duration::from_millis(100)).await;

    // Create and send second feed
    let mut prices2 = HashMap::new();
    prices2.insert(
        "******************************************".to_string(), // WETH
        2100.0,
    );
    prices2.insert(
        "******************************************".to_string(), // USDC
        1.0,
    );

    let feed2 = Feed {
        prices: prices2,
        source: "other_dex".to_string(),
        timestamp: OffsetDateTime::now_utc().unix_timestamp() as u64,
        supported_chains: vec!["ethereum".into()].into_iter().collect(),
    };

    tx.send(feed2).expect("Failed to send feed");

    let mut received_feeds = Vec::new();
    let mut quote_response_received = false;

    while let Some(Ok(msg)) = read.next().await {
        if let tokio_tungstenite::tungstenite::Message::Text(msg_str) = msg {
            if let Ok(feed) = serde_json::from_str::<Feed>(&msg_str) {
                received_feeds.push(feed);
            } else if let Ok(_) = serde_json::from_str::<QuoteResponse>(&msg_str) {
                quote_response_received = true;
            }
        }

        if received_feeds.len() >= 2 && quote_response_received {
            break;
        }
    }

    assert_eq!(received_feeds.len(), 2);
    assert!(received_feeds.iter().any(|f| f.source == "1inch"));
    assert!(received_feeds.iter().any(|f| f.source == "other_dex"));
    assert!(quote_response_received);
}

#[tokio::test]
async fn test_database_storage() {
    // dotenv::dotenv().ok();
    let shared_feeds: SharedFeeds = Arc::new(Mutex::new(HashMap::new()));

    // Create a test publisher
    let (tx, _) = broadcast::channel::<Feed>(100);
    let tx = Arc::new(tx);
    let publisher = ChannelPublisher::new(tx);

    // Create a test feed
    let mut prices = HashMap::new();
    prices.insert(
        "******************************************".to_string(), // WETH
        2000.0,
    );
    prices.insert(
        "******************************************".to_string(), // USDC
        1.0,
    );

    let feed = Feed {
        prices,
        source: "test_source".to_string(),
        timestamp: OffsetDateTime::now_utc().unix_timestamp() as u64,
        supported_chains: vec!["ethereum".into()].into_iter().collect(),
    };

    // Publish the feed with the shared_feeds
    publisher
        .publish(feed.clone(), shared_feeds)
        .await
        .expect("Failed to publish feed");
}
