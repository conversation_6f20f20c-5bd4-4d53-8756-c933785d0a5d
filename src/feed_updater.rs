use crate::{errors, feed::Feed, feed_publisher::FeedPublisher, SharedFeeds};
use log::error;
use tokio::time::{self, Duration};

pub trait FeedUpdater: Send + Sync {
    fn get_frequency(&self) -> u64;
    fn update(
        &self,
        chain_name: String,
    ) -> impl std::future::Future<Output = Result<Feed, errors::Error>> + Send;

    fn run_periodic_updates<'a>(
        &'a self,
        publisher: &'a (impl FeedPublisher + Send + Sync),
        shared_feeds: SharedFeeds,
        chain_name: String,
    ) -> impl std::future::Future<Output = ()> + Send + 'a {
        async move {
            let frequency = self.get_frequency();
            let mut interval = time::interval(Duration::from_millis(frequency));

            loop {
                interval.tick().await;
                match self.update(chain_name.clone()).await {
                    Ok(feed) => {
                        // info!("Feed updated: {:?}", feed);
                        if let Err(e) = publisher.publish(feed, shared_feeds.clone()).await {
                            error!("WS Feed Publishing error: {}", e);
                        }
                    }
                    Err(e) => error!("Feed Update error for {}: {}", chain_name, e),
                }
            }
        }
    }
}
