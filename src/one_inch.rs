use std::{
    collections::{HashMap, HashSet},
    sync::Arc,
};

use futures_util::future::join_all;
use log::{info, warn};
use serde::Deserialize;
use time::OffsetDateTime;
use tokio::sync::broadcast;
use tokio::time::{sleep, Duration};

use crate::{errors, feed::Feed, feed_updater::FeedUpdater};
pub struct OneInch {
    update_frequency_millis: u64,
    tracked_tokens: HashSet<String>,
    tx: Arc<broadcast::Sender<Feed>>,
    api_key: String,
    chain_id: u64,
    supported_chains: HashSet<String>,
}

#[derive(Debug, Deserialize)]
struct PriceResponse {
    #[serde(flatten)]
    prices: HashMap<String, String>,
}

impl OneInch {
    pub fn new(
        tracked_tokens: HashSet<String>,
        update_frequency_millis: u64,
        tx: Arc<broadcast::Sender<Feed>>,
        api_key: String,
        chain_id: u64,
        supported_chains: HashSet<String>,
    ) -> Self {
        Self {
            update_frequency_millis,
            tracked_tokens,
            tx,
            api_key,
            chain_id,
            supported_chains,
        }
    }

    pub async fn get_token_prices(
        &self,
        tokens: &[&str],
    ) -> Result<HashMap<String, f64>, errors::Error> {
        info!("chain id {:?}", self.chain_id);
        let client = reqwest::Client::new();

        let mut tasks: Vec<tokio::task::JoinHandle<Result<HashMap<String, f64>, errors::Error>>> =
            Vec::new();

        for chunk in tokens.chunks(45) {
            let chunk = chunk.iter().map(|&s| s.to_string()).collect::<Vec<_>>();
            let client = client.clone();
            let chain_id = self.chain_id.clone();
            let api_key = self.api_key.clone();

            tasks.push(tokio::spawn(async move {
                let mut retries = 3;
                let mut chunk_prices = HashMap::new();

                while retries > 0 {
                    let url = format!(
                        "https://api.1inch.dev/price/v1.1/{}/{}?currency=USD",
                        chain_id,
                        chunk.join(",")
                    );

                    // info!("Requesting prices from URL: {}", url);

                    let response = client
                        .get(&url)
                        .header("Authorization", format!("Bearer {}", api_key))
                        .send()
                        .await;

                    if let Ok(resp) = response {
                        if resp.status().is_success() {
                            if let Ok(price_response) = resp.json::<PriceResponse>().await {
                                for token in &chunk {
                                    if let Some(price_str) = price_response.prices.get(token) {
                                        if let Ok(price) = price_str.parse::<f64>() {
                                            if price != 0.0 {
                                                chunk_prices.insert(token.clone(), price);
                                            }
                                        }
                                    }
                                }
                                break; // Break on success
                            }
                        }
                    }

                    retries -= 1;
                    if retries > 0 {
                        warn!("Retrying chunk in 3 seconds for {}", chain_id);
                        sleep(Duration::from_secs(3)).await;
                    }
                }

                Ok(chunk_prices)
            }));
        }

        let results = join_all(tasks).await;

        let mut prices = HashMap::new();
        for result in results {
            match result {
                Ok(Ok(chunk_prices)) => {
                    prices.extend(chunk_prices);
                }
                Ok(Err(_)) | Err(_) => {
                    warn!("Failed to fetch prices for one chunk, continuing with collected data.");
                }
            }
        }

        if prices.is_empty() {
            return Err(errors::Error::OneInch(
                "No valid prices received".to_string(),
            ));
        }

        Ok(prices)
    }
}

impl FeedUpdater for OneInch {
    fn get_frequency(&self) -> u64 {
        self.update_frequency_millis
    }

    fn update(
        &self,
        chain_name: String,
    ) -> impl std::future::Future<Output = Result<Feed, errors::Error>> + Send {
        async move {
            let prices = self
                .get_token_prices(
                    &self
                        .tracked_tokens
                        .iter()
                        .map(|t| t.as_str())
                        .collect::<Vec<_>>()
                        .as_slice(),
                )
                .await?;

            Ok(Feed {
                prices,
                source: "one_inch".to_string(),
                timestamp: OffsetDateTime::now_utc().unix_timestamp() as u64,
                supported_chains: vec![chain_name].into_iter().collect(),
            })
        }
    }
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::util::env_var;

    #[tokio::test]
    async fn test_get_token_prices() {
        let api_key = env_var("ONEINCH_API_KEY").unwrap();
        let (tx, _) = broadcast::channel::<Feed>(100);
        let tx = Arc::new(tx);

        let one_inch = OneInch::new(
            HashSet::new(),
            60_000,
            tx,
            api_key,
            1,
            vec!["ethereum".into()].into_iter().collect(),
        );

        // Include both USDC and WETH addresses
        let tokens = &[
            "******************************************", // USDC
            "******************************************", // WETH
        ];

        let result = one_inch.get_token_prices(tokens).await;

        // assert!(result.is_ok());
        // let prices = result.unwrap();
        // assert!(!prices.is_empty());
    }
}
