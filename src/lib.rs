use crate::{
    constants::ETHEREUM_USDC_ADDRESS, db::connect_to_db, feed::Feed, feed_publisher::FeedPublisher, util::get_chain_id
};
use actix_cors::Cors;
use async_trait;
use feed_updater::FeedUpdater;
use firestore::{FirestoreDb, FirestoreDbOptions};
use futures_util::{SinkExt, StreamExt, TryStreamExt};
use log::{error, info, warn};
use structs::{QuoteRequest, QuoteResponse, TokenData};
use std::{collections::{HashMap, HashSet}, sync::{Arc, Mutex}, time::Duration};
use time::OffsetDateTime;
use tokio::{join, net::TcpStream, sync::broadcast, time::sleep};
use tokio_tungstenite::accept_async;

pub mod debridge;
pub mod errors;
pub mod feed;
pub mod feed_publisher;
pub mod feed_updater;
pub mod jup;
pub mod one_inch;
pub mod structs;
mod util;
mod constants;
mod db;
mod routes;
mod handlers;

use crate::util::env_var;
use actix_web::{web, App, HttpServer};

pub type SharedFeeds = Arc<Mutex<HashMap<String, Feed>>>;

pub async fn main() {
    // Set up logging
    env_logger::init();
    let db_client = Arc::new(connect_to_db().await.unwrap());

    let shared_feeds: SharedFeeds = Arc::new(Mutex::new(HashMap::new()));
    // Create broadcast channel for price feeds
    let (tx, _) = broadcast::channel::<Feed>(100);
    let tx = Arc::new(tx);

    let shared_feeds_api = shared_feeds.clone();

    let mut feeds_map = HashMap::new();

    let firestore_project_id = env_var("FIRESTORE_PROJECT_ID").unwrap();
    let google_app_credentials = env_var("GOOGLE_APPLICATION_CREDENTIALS").unwrap().into();

    let firebase_db = FirestoreDb::with_options_service_account_key_file(
        FirestoreDbOptions::new(firestore_project_id),
        google_app_credentials,
    )
    .await
    .unwrap();

    let tokenstream = firebase_db
        .fluent()
        .select()
        .from("tokens")
        .filter(|q| q.field("isViewOnly").eq(false))
        // .limit(4)
        .obj()
        .stream_query_with_errors()
        .await
        .expect("Failed to query firestore");

    let tokens: Vec<TokenData> = tokenstream.try_collect().await.unwrap();
    let mut chain_names: Vec<String> = Vec::new();
    let mut groups_map: HashMap<String, Vec<String>> = HashMap::new();

    for token in tokens.iter() {
        for chain in token.chains.iter() {
            if chain.id == String::from("solana") {
                groups_map
                    .entry(chain.id.clone())
                    .or_insert_with(Vec::new)
                    .push(chain.address.clone());
            } else {
                groups_map
                    .entry(chain.id.clone())
                    .or_insert_with(Vec::new)
                    .push(chain.address.clone().to_lowercase());
            }

            if !chain_names.contains(&chain.id) {
                chain_names.push(chain.id.clone());
            }
        }
    }

    // Ensure USDC is first if present
    for group in groups_map.values_mut() {
        if let Some(pos) = group.iter().position(|addr| addr == ETHEREUM_USDC_ADDRESS) {
            let usdc = group.remove(pos);
            group.insert(0, usdc);
        }
    }

    let sleep_interval = 60_000 / 5; // Spread across 1 minute

    for chain_name in chain_names {
        if chain_name == String::from("solana") {
            continue;
        }

        let chain_id = match get_chain_id(&chain_name) {
            Some(id) => id,
            None => continue,
        };

        let api_key = env_var("ONEINCH_API_KEY").expect("ONEINCH_API_KEY key not found in environment");
        let one_inch = one_inch::OneInch::new(
            groups_map
                .get(&chain_name)
                .unwrap()
                .into_iter()
                .cloned()
                .collect::<HashSet<_>>(),
            60_000, // 1 minute
            tx.clone(),
            api_key,
            chain_id,
            vec![chain_name.clone()].into_iter().collect(),
        );

        let publisher = ChannelPublisher::new(tx.clone());
        let feeds = shared_feeds.clone();

        feeds_map.insert(chain_name, (one_inch, publisher, feeds));
    }

    for (chain_name, (one_inch, publisher, feeds)) in feeds_map.into_iter() {
        sleep(Duration::from_millis(sleep_interval as u64)).await; // Sleep before each init except the first

        let publisher = publisher.clone();
        let feeds = feeds.clone();

        // info!("Start feed for chain name {:?}", chain_name);
        tokio::spawn(async move {
            one_inch
                .run_periodic_updates(&publisher, feeds, chain_name.to_string())
                .await;
        });
    }

    // // Create publishers
    let publisher_jup = ChannelPublisher::new(tx.clone());
    let feeds_jup = shared_feeds.clone();

    // // Initialize Jupiter with SOL token
    let jup = jup::Jup::new(
        groups_map
            .get("solana")
            .unwrap()
            .into_iter()
            .cloned()
            .collect::<HashSet<_>>(),
        10_000, // 1 minute
        tx.clone(),
        vec!["solana".into()].into_iter().collect(),
    );

    tokio::spawn(async move {
        jup.run_periodic_updates(&publisher_jup, feeds_jup, String::from("solana"))
            .await;
    });

    // Start the API server
    let api_server = async {
        HttpServer::new(move || {
            let cors = Cors::default()
                .allow_any_origin()
                .allowed_methods(vec!["GET", "POST"])
                .allow_any_header()
                .max_age(3600);

            App::new()
                .wrap(cors)
                .app_data(web::Data::new(shared_feeds_api.clone()))
                .app_data(web::Data::new(Arc::clone(&db_client)))
                .configure(routes::init_routes)
        })
        .bind("0.0.0.0:8087")
        .unwrap()
        .run()
        .await
    };

    // Set up WebSocket server
    let websocket_server = async {
        let addr = "0.0.0.0:8084";
        let listener = tokio::net::TcpListener::bind(addr)
            .await
            .expect("Failed to bind");
        info!("WebSocket server listening on: {}", addr);

        // // Accept WebSocket connections
        while let Ok((stream, _)) = listener.accept().await {
            let tx = tx.clone();
            tokio::spawn(async move {
                crate::handle_connection(stream, tx).await;
            });
        }
        Ok::<(), std::io::Error>(())
    };

    let (rest_result, _ws_result) = join!(api_server, websocket_server);
    rest_result.unwrap();
    // ws_result.unwrap();
}



#[derive(Debug, Clone)]
pub struct ChannelPublisher {
    pub tx: Arc<broadcast::Sender<Feed>>,
}

impl ChannelPublisher {
    pub fn new(tx: Arc<broadcast::Sender<Feed>>) -> Self {
        Self { tx }
    }
}

#[async_trait::async_trait]
impl FeedPublisher for ChannelPublisher {
    async fn publish(&self, feed: Feed, _shared_feeds: SharedFeeds) -> Result<(), errors::Error> {
        // Broadcast the feed as before
        match self.tx.send(feed) {
            Ok(_) => Ok(()),
            Err(e) => {
                warn!("Failed to send feed, mostly happens if a receiver is not connected to the feed websockets yet: {}", e);
                Ok(())
            }
        }
    }
}

pub async fn handle_connection(stream: TcpStream, tx: Arc<broadcast::Sender<Feed>>) {
    let ws_stream = accept_async(stream)
        .await
        .expect("Failed to accept websocket");
    let (mut ws_sender, mut ws_receiver) = ws_stream.split();
    let mut rx = tx.subscribe();

    let (quote_tx, mut quote_rx) = tokio::sync::mpsc::channel(100);

    // Handle incoming quote requests
    let mut rx_1 = tx.subscribe();
    let quote_handler = tokio::spawn(async move {
        let mut latest_feeds: HashMap<String, Feed> = HashMap::new();

        while let Some(msg) = ws_receiver.next().await {
            if let Ok(msg) = msg {
                if let Ok(request) = serde_json::from_str::<QuoteRequest>(&msg.to_string()) {
                    // First drain any pending updates to get latest state
                    while let Ok(feed) = rx_1.try_recv() {
                        for chain in &feed.supported_chains {
                            latest_feeds.insert(chain.clone(), feed.clone());
                        }
                    }

                    // Wait for feeds that support both chains with timeout
                    let timeout = tokio::time::sleep(Duration::from_secs(65));
                    tokio::pin!(timeout);

                    // If we don't have suitable feeds yet, wait for them
                    while !latest_feeds.contains_key(&request.from_chain)
                        || !latest_feeds.contains_key(&request.to_chain)
                    {
                        tokio::select! {
                            result = rx_1.recv() => {
                                match result {
                                    Ok(feed) => {
                                        for chain in &feed.supported_chains {
                                            latest_feeds.insert(chain.clone(), feed.clone());
                                        }
                                    }
                                    _ => break,
                                }
                            }
                            _ = &mut timeout => {
                                error!("Timeout waiting for feeds for chains: {} and {}",
                                    request.from_chain, request.to_chain);
                                let timeout_response = QuoteResponse {
                                    from_chain: request.from_chain.clone(),
                                    to_chain: request.to_chain.clone(),
                                    from_token: request.from_token.clone(),
                                    to_token: request.to_token.clone(),
                                    from_amount: request.from_amount.clone(),
                                    to_amount: None,
                                    timestamp: OffsetDateTime::now_utc(),
                                };
                                if let Ok(response_json) = serde_json::to_string(&timeout_response) {
                                    let _ = quote_tx
                                        .send(tokio_tungstenite::tungstenite::Message::Text(
                                            response_json.into(),
                                        ))
                                        .await;
                                }
                                continue;
                            }
                        }
                    }

                    // Get the relevant feeds for both chains
                    let source_feed = latest_feeds.get(&request.from_chain).unwrap();
                    let dest_feed = latest_feeds.get(&request.to_chain).unwrap();

                    // Calculate quote using both feeds
                    let from_price = source_feed.prices.get(&request.from_token);
                    let to_price = dest_feed.prices.get(&request.to_token);
                    // info!(
                    //     "Source price: {:?}, Destination price: {:?}",
                    //     from_price,
                    //     to_price
                    // );

                    let response = match (from_price, to_price) {
                        (Some(from), Some(to)) => {
                            if let Ok(amount) = request.from_amount.parse::<f64>() {
                                QuoteResponse {
                                    from_chain: request.from_chain.clone(),
                                    to_chain: request.to_chain.clone(),
                                    from_token: request.from_token.clone(),
                                    to_token: request.to_token.clone(),
                                    from_amount: request.from_amount.clone(),
                                    to_amount: Some(((amount * from) / to).to_string()),
                                    timestamp: OffsetDateTime::now_utc(),
                                }
                            } else {
                                error!("Invalid amount format: {}", request.from_amount);
                                QuoteResponse {
                                    from_chain: request.from_chain,
                                    to_chain: request.to_chain,
                                    from_token: request.from_token,
                                    to_token: request.to_token,
                                    from_amount: request.from_amount,
                                    to_amount: None,
                                    timestamp: OffsetDateTime::now_utc(),
                                }
                            }
                        }
                        _ => {
                            error!(
                                "Missing price for tokens: from={}, to={}",
                                request.from_token, request.to_token
                            );
                            QuoteResponse {
                                from_chain: request.from_chain,
                                to_chain: request.to_chain,
                                from_token: request.from_token,
                                to_token: request.to_token,
                                from_amount: request.from_amount,
                                to_amount: None,
                                timestamp: OffsetDateTime::now_utc(),
                            }
                        }
                    };

                    if let Ok(response_json) = serde_json::to_string(&response) {
                        let _ = quote_tx
                            .send(tokio_tungstenite::tungstenite::Message::Text(
                                response_json.into(),
                            ))
                            .await;
                    }
                }
            }
        }
    });

    // Handle feed updates and quote responses
    loop {
        tokio::select! {
            Ok(msg) = rx.recv() => {
                let msg_json = serde_json::to_string(&msg).unwrap();
                let ws_msg = tokio_tungstenite::tungstenite::Message::text(msg_json);
                if let Err(e) = ws_sender.send(ws_msg).await {
                    warn!("Failed to send message: {}", e);
                    break;
                }
            }
            Some(quote_msg) = quote_rx.recv() => {
                if let Err(e) = ws_sender.send(quote_msg).await {
                    warn!("Failed to send quote response: {}", e);
                    break;
                }
            }
        }
    }

    let _ = quote_handler.abort();
}