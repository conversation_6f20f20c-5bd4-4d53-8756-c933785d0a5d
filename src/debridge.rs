use log::info;
use std::{collections::HashSet, sync::Arc};
use tokio::sync::broadcast;

use crate::{constants::{DEBRIDGE_SOLANA_CHAIN_ID, SOLANA_CHAIN_ID}, errors, feed::Feed, structs::{CreateOrderRequest, CreateOrderResponse}};

#[allow(dead_code)]
pub struct DeBridge {
    tracked_tokens: HashSet<String>,
    tx: Arc<broadcast::Sender<Feed>>,
    chain_id: u64,
}

impl DeBridge {
    pub fn new(
        tracked_tokens: HashSet<String>,
        tx: Arc<broadcast::Sender<Feed>>,
        chain_id: u64,
    ) -> Self {
        Self {
            tracked_tokens,
            tx,
            chain_id,
        }
    }

    pub async fn create_order(
        &self,
        request: CreateOrderRequest,
    ) -> Result<CreateOrderResponse, errors::Error> {
        const API_URL: &str = "https://dln.debridge.finance/v1.0/dln";
        let client = reqwest::Client::new();

        let src_chain_id = if request.src_chain_id == SOLANA_CHAIN_ID.to_string() {
            DEBRIDGE_SOLANA_CHAIN_ID.to_string()
        } else {
            request.src_chain_id
        };

        let dst_chain_id = if request.dst_chain_id == SOLANA_CHAIN_ID.to_string() {
            DEBRIDGE_SOLANA_CHAIN_ID.to_string()
        } else {
            request.dst_chain_id
        };

        let response = client
            .get(&format!("{}/order/create-tx", API_URL))
            .header("accept", "application/json")
            .query(&[
                ("srcChainId", &src_chain_id),
                ("srcChainTokenIn", &request.src_token),
                ("srcChainTokenInAmount", &request.amount),
                ("dstChainId", &dst_chain_id),
                ("dstChainTokenOut", &request.dst_token),
                ("dstChainTokenOutAmount", &request.dst_amount),
                ("prependOperatingExpenses", &"false".to_string()),
                ("skipSolanaRecipientValidation", &"false".to_string()),
                ("additionalTakerRewardBps", &"0".to_string()),
            ])
            .send()
            .await
            .map_err(|e| errors::Error::DeBridge(format!("Request failed: {}", e)))?;

        if !response.status().is_success() {
            let response_status = response.status();
            let error_body = response
                .text()
                .await
                .unwrap_or_else(|_| "Unable to read error response".to_string());
            return Err(errors::Error::DeBridge(format!(
                "API returned error status: {} - {}",
                response_status, error_body
            )));
        }

        // Print the response body before trying to parse it
        let body = response
            .text()
            .await
            .map_err(|e| errors::Error::DeBridge(format!("Failed to get response body: {}", e)))?;
        info!("Response body: {}", body);

        // Parse the printed response
        serde_json::from_str(&body)
            .map_err(|e| errors::Error::DeBridge(format!("Failed to parse response: {}", e)))
    }

    pub async fn get_quote(
        &self,
        from_chain: &str,
        to_chain: &str,
        from_token: &str,
        to_token: &str,
        amount: &str,
    ) -> Result<CreateOrderResponse, errors::Error> {
        let request = CreateOrderRequest {
            src_chain_id: from_chain.to_string(),
            src_token: from_token.to_string(),
            amount: amount.to_string(),
            dst_chain_id: to_chain.to_string(),
            dst_token: to_token.to_string(),
            receiver: "******************************************".to_string(),
            dst_amount: "auto".to_string(),
        };

        self.create_order(request).await
    }

    pub async fn get_token_out_amount(
        &self,
        from_chain: &str,
        to_chain: &str,
        from_token: &str,
        to_token: &str,
        amount: &str,
    ) -> Result<(String, f64, f64), errors::Error> {
        let quote: CreateOrderResponse = self
            .get_quote(from_chain, to_chain, from_token, to_token, amount)
            .await?;
        let token_in_usd_price = quote.estimation.src_chain_token_in.approximate_usd_value
            / quote
                .estimation
                .src_chain_token_in
                .amount
                .parse::<f64>()
                .unwrap();
        let expense_amount = match quote
            .estimation
            .src_chain_token_in
            .approximate_operating_expense
        {
            Some(expense) => expense.parse::<f64>().unwrap(),
            None => 0.0,
        };
        let expense_usd_amount = expense_amount * token_in_usd_price;
        let source_token_approximate_usd_value =
            quote.estimation.src_chain_token_in.approximate_usd_value - expense_usd_amount;
        Ok((
            quote.estimation.dst_chain_token_out.amount,
            source_token_approximate_usd_value,
            quote.estimation.dst_chain_token_out.approximate_usd_value,
        ))
    }
}

#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_get_quote() {
        let (tx, _) = broadcast::channel::<Feed>(100);
        let tx = Arc::new(tx);

        let debridge = DeBridge::new(HashSet::new(), tx, 1);

        let result = debridge
            .get_quote(
                "1",
                "56",
                "******************************************", // Native ETH
                "******************************************", // BUSD on BSC
                "100000000000000000",                         // 0.1 ETH
            )
            .await;

        if let Err(ref e) = result {
            info!("Error: {:?}", e);
        }
        assert!(result.is_ok());
        let quote = result.unwrap();
        assert!(quote.estimation.dst_chain_token_out.approximate_usd_value > 0.0);
    }

    #[tokio::test]
    async fn test_get_token_out_amount() {
        let (tx, _) = broadcast::channel::<Feed>(100);
        let tx = Arc::new(tx);

        let debridge = DeBridge::new(HashSet::new(), tx, 1);

        let result = debridge
            .get_token_out_amount(
                "1",
                "56",
                "******************************************", // Native ETH
                "******************************************", // BUSD on BSC
                "100000000000000000",                         // 0.1 ETH
            )
            .await;

        if let Err(ref e) = result {
            info!("Error: {:?}", e);
        }
        assert!(result.is_ok());
        let (amount, approx_source_usd, approx_dest_usd) = result.unwrap();
        info!("Output amount: {}", amount);
        assert!(!amount.is_empty());
    }
}
