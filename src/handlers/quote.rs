use std::{collections::HashSet, sync::Arc};
use actix_web::{web::{Json, Data}, HttpResponse};
use tokio_postgres::Client;
use log::{info, warn};
use serde_json::json;
use tokio::sync::broadcast;
use crate::{constants::{ETH_WEI_MULTIPLIER, SOLANA_CHAIN_ID, SOL_LAMPORTS_MULTIPLIER}, debridge::DeBridge, feed::Feed, structs::{AmountTypes, JupQuote, OneInchQuote, QuoteRequest, ResultCosts, ThirdPartyFeeResult, TransactionCost}, util::{calculate_price_impact, env_var, get_stable_coin_address, get_token_decimals, get_usd_value, get_wrapped_token_address}};

pub async fn handle_get_quote(
    db_client: Data<Arc<Client>>,
    req: Json<QuoteRequest>
) -> HttpResponse {
    let solana_id = String::from(SOLANA_CHAIN_ID);
    let request_client = reqwest::Client::new();
    let gas_calculator_base_url = env_var("GAS_CALCULATOR_URL").expect("GAS_CALCULATOR_URL key not found in environment");

    let token_in_decimals = get_token_decimals(&req.from_chain, &req.from_token).await;
    info!("token_in_decimals = {:?}", token_in_decimals);
    info!("from_amount = {:?}", req.from_amount);
    let amount_in_formatted =
        (req.from_amount.parse::<f64>().unwrap() / 10.0_f64.powf(token_in_decimals as f64));
    info!("amount_in_formatted = {:?}", amount_in_formatted);

    let amount_in_usd_price = if let Some((res, _)) = process_tokens_from_launchpad(db_client.clone(), &req.from_token, &req.from_chain.parse::<i64>().unwrap()).await {
        info!("amount_in_usd_price from launchpad = {:?}", res);
        res
    } else {
        let price = get_usd_value(Some(&req.from_token), &req.from_chain).await;
        info!("amount_in_usd_price from get_usd_value = {:?}", price);
        price
    };

    info!("amount_in_usd_price = {:?}", amount_in_usd_price);

    let amount_in_usd = amount_in_usd_price * amount_in_formatted;
    info!("amount_in_usd = {:?}", amount_in_usd);
    info!("amount_in_formatted = {:?}", amount_in_formatted);

    let fee_payload = json!({
        "from_chain": req.from_chain.parse::<u64>().unwrap(),
        "to_chain": req.to_chain.parse::<u64>().unwrap(),
        "amount_in": req.from_amount,
        "amount_in_usd": amount_in_usd,
        "token_in": req.from_token,
        "token_out": req.to_token
    });

    let gas_endpoint = format!("{}/gas", gas_calculator_base_url);
    let response_fee_data: Option<TransactionCost> = match request_client
        .post(gas_endpoint)
        .json(&fee_payload)
        .send()
        .await
    {
        Ok(data) => {
            let data_json: TransactionCost = data.json().await.unwrap();
            info!("data json response {:?}", data_json);
            Some(data_json)
        }
        Err(_e) => {
            warn!("error in response fee data {:?}", _e);
            None
        }
    };

    let from_amount = req.from_amount.parse::<u128>().unwrap()
        - response_fee_data
            .clone()
            .unwrap()
            .inclusive_layer_fee
            .parse::<u128>()
            .unwrap();

    let native_usd = get_usd_value(None, &req.from_chain).await;

    if req.from_chain == req.to_chain {
        // same chain
        if req.from_chain == solana_id {
            let mut result_data: ResultCosts = structure_result(
                &response_fee_data,
                native_usd,
                SOL_LAMPORTS_MULTIPLIER,
                SOL_LAMPORTS_MULTIPLIER,
                false,
            );

            if req.from_token == req.to_token {
                if native_usd == 0.0 {
                    info!("returning response for jup as native usd got 0");
                    return HttpResponse::Ok().json(
                        json!({ "amount": from_amount.to_string(), "fee_data": response_fee_data }),
                    );
                }
                // withdraw; requires no swap
                return HttpResponse::Ok().json(json!({ "amount": from_amount.to_string(), "fee_data": result_data, "price_impact": 0.0 }));
            }

            // if requested quote for launchpad
            if let Some((amount_out_quote, amount_out_quote_usd)) = get_launchpad_amount_out_quote(db_client.clone(), &req.to_token, &req.to_chain.parse::<i64>().unwrap(), &amount_in_usd).await {
                result_data.provider_fee.provider = "Launchpad".to_string();
                HttpResponse::Ok().json(json!({ "amount": amount_out_quote.to_string(), "amount_out_usd": amount_out_quote_usd.to_string(), "fee_data": result_data, "price_impact": "0.0" }))
            } else {
                match get_jup_quote(
                    &req.from_chain,
                    &req.from_token,
                    &req.to_token,
                    &from_amount.to_string(),
                )
                .await
                {
                    Ok(res) => {
                        if native_usd == 0.0 {
                            info!("returning response for jup as native usd got 0");
                            return HttpResponse::Ok().json(
                                json!({ "amount": res.out_amount, "fee_data": response_fee_data }),
                            );
                        }
    
                        let result_data: ResultCosts = structure_result(
                            &response_fee_data,
                            native_usd,
                            SOL_LAMPORTS_MULTIPLIER,
                            SOL_LAMPORTS_MULTIPLIER,
                            false,
                        );
                        let price_impact = res.price_impact_pct.parse::<f64>().unwrap();
                        HttpResponse::Ok().json(json!({ "amount": res.out_amount, "fee_data": result_data, "price_impact": price_impact }))
                    }
                    Err(e) => HttpResponse::BadRequest().json(json!({ "error": e.to_string() })),
                }
            }
        } else {
            let mut result_data = structure_result(
                &response_fee_data,
                native_usd,
                ETH_WEI_MULTIPLIER,
                ETH_WEI_MULTIPLIER,
                false,
            );

            if req.from_token == req.to_token {
                if native_usd == 0.0 {
                    info!("returning response for 1inch as native usd got 0");
                    return HttpResponse::Ok().json(
                        json!({ "amount": from_amount.to_string(), "fee_data": response_fee_data }),
                    );
                }
                // withdraw; requires no swap
                return HttpResponse::Ok().json(json!({ "amount": from_amount.to_string(), "fee_data": result_data, "price_impact": 0.0 }));
            }
            // if requested quote for launchpad
            if let Some((amount_out_quote, amount_out_quote_usd)) = get_launchpad_amount_out_quote(db_client.clone(), &req.to_token, &req.to_chain.parse::<i64>().unwrap(), &amount_in_usd).await {
                result_data.provider_fee.provider = "Launchpad".to_string();
                HttpResponse::Ok().json(json!({ "amount": amount_out_quote.to_string(), "amount_out_usd": amount_out_quote_usd.to_string(), "fee_data": result_data, "price_impact": "0.0" }))
            } else {
                match get_1_inch_quote(
                    &req.from_chain,
                    &req.from_token,
                    &req.to_token,
                    &from_amount.to_string(),
                )
                .await
                {
                    Ok(res) => {
                        info!("returning 1inch quote");
                        if native_usd == 0.0 {
                            info!("returning response for 1inch as native usd got 0");
                            return HttpResponse::Ok()
                                .json(json!({ "amount": res.amount, "fee_data": response_fee_data }));
                        }
    
                        let price_impact = calculate_price_impact(
                            &from_amount.to_string().parse::<f64>().unwrap(),
                            &res.amount.parse::<f64>().unwrap(),
                            &req.from_chain,
                            &req.to_chain,
                            &req.from_token,
                            &req.to_token,
                        )
                        .await;
                        info!("price impact local swap {:?}", price_impact);
    
                        HttpResponse::Ok().json(json!({ "amount": res.amount, "fee_data": result_data, "price_impact": price_impact }))
                    }
                    Err(e) => HttpResponse::BadRequest().json(json!({ "error": e.to_string() })),
                }   
            }
        }
    } else {
        // cross chain

        let pre_swap_stable_coin_address = get_stable_coin_address(&req.from_chain);
        if pre_swap_stable_coin_address.len() == 0 {
            return HttpResponse::BadRequest()
                .json(json!({ "error": "No stable coin address found for preswap".to_string() }));
        }

        let pre_swap_amount;
        if req.from_chain == solana_id {
            if req.from_token.to_lowercase() != pre_swap_stable_coin_address.to_lowercase() {
                let pre_swap_quote = match get_jup_quote(
                    &req.from_chain,
                    &req.from_token,
                    pre_swap_stable_coin_address,
                    &from_amount.to_string(),
                )
                .await
                {
                    Ok(quote) => quote.out_amount,
                    Err(_) => String::new(),
                };
                pre_swap_amount = pre_swap_quote;
            } else {
                pre_swap_amount = from_amount.to_string().clone();
            }
        } else {
            if req.from_token.to_lowercase() != pre_swap_stable_coin_address.to_lowercase() {
                let pre_swap_quote = match get_1_inch_quote(
                    &req.from_chain,
                    &req.from_token,
                    pre_swap_stable_coin_address,
                    &from_amount.to_string(),
                )
                .await
                {
                    Ok(quote) => quote.amount,
                    Err(_) => String::new(),
                };
                pre_swap_amount = pre_swap_quote;
            } else {
                pre_swap_amount = from_amount.to_string().clone();
            }
        }

        if pre_swap_amount.len() == 0 {
            return HttpResponse::BadRequest()
                .json(json!({ "error": "No preswap amount found".to_string() }));
        }

        let (tx, _) = broadcast::channel::<Feed>(100);
        let tx = Arc::new(tx);

        let debridge = DeBridge::new(HashSet::new(), tx.clone(), 1);

        match debridge
            .get_token_out_amount(
                &req.from_chain,
                &req.to_chain,
                pre_swap_stable_coin_address,
                &req.to_token,
                &pre_swap_amount,
            )
            .await
        {
            Ok(quote) => {
                let (amount, source_amount_usd, dst_amount_usd) = quote;
                let src_native_usd = get_usd_value(None, &req.from_chain).await;
                let dst_native_usd = get_usd_value(None, &req.to_chain).await;

                if src_native_usd == 0.0 || dst_native_usd == 0.0 {
                    info!("returning response for debridge as native usd got 0");
                    return HttpResponse::Ok()
                        .json(json!({ "amount": amount, "fee_data": response_fee_data }));
                }

                let src_denom;
                let dst_denom;

                if req.from_chain == solana_id {
                    src_denom = SOL_LAMPORTS_MULTIPLIER;
                } else {
                    src_denom = ETH_WEI_MULTIPLIER;
                }

                if req.to_chain == solana_id {
                    dst_denom = SOL_LAMPORTS_MULTIPLIER;
                } else {
                    dst_denom = ETH_WEI_MULTIPLIER;
                }

                let price_impact = (source_amount_usd - dst_amount_usd) / source_amount_usd;
                info!("price impact cross chain swap {:?}", price_impact);
                let result_data =
                    structure_result(&response_fee_data, native_usd, src_denom, dst_denom, true);

                HttpResponse::Ok().json(json!({ "amount": amount, "fee_data": result_data, "price_impact": price_impact }))
            }
            Err(e) => HttpResponse::BadRequest().json(json!({ "error": e.to_string() })),
        }
    }
}

async fn get_launchpad_amount_out_quote(
    db_client: Data<Arc<Client>>,
    launchpad_token: &str,
    to_chain: &i64,
    amount_in_usd: &f64
) -> Option<(f64, f64)> {
    if let Some((amount_out_usd_price, launchpad_token_decimals)) = process_tokens_from_launchpad(db_client, launchpad_token, to_chain).await {
        info!("amount_in_usd = {:?}", amount_in_usd);
        let amount_out_quote = amount_in_usd / amount_out_usd_price;
        info!("amount_out_quote = {:?}", amount_out_quote);
        let amount_out_quote_usd = amount_out_quote * amount_out_usd_price;
        info!("amount_out_quote_usd = {:?}", amount_out_quote_usd);
        return Some((amount_out_quote * 10.0_f64.powf(launchpad_token_decimals), amount_out_quote_usd));
    } else {
        None
    }
}

// gets launchpad token usd price
async fn process_tokens_from_launchpad(
    db_client: Data<Arc<Client>>,
    launchpad_token: &str,
    to_chain: &i64,
) -> Option<(f64, f64)> {
    let token_out_info_query = "SELECT ammswap.timestamp, pools.chain_id, ammswap.sqrt_price, pools.pool_address, pools.token_0_address, pools.token_1_address, pools.initial_sqrt_price FROM pools LEFT JOIN ammswap ON LOWER(ammswap.pool_address) = LOWER(pools.pool_address) AND ammswap.chain_id = pools.chain_id WHERE LOWER(pools.token_1_address) = LOWER($1) OR LOWER(pools.token_0_address) = LOWER($1) AND pools.chain_id = $2 ORDER BY ammswap.timestamp DESC NULLS LAST LIMIT 1";
    let token_out_info_response = db_client.query_one(token_out_info_query, &[&launchpad_token, &to_chain]).await;
    let token_out_sqrt_price;
    let token_out_usd_price: f64;

    match token_out_info_response {
        Ok(row) => {
            if let Some(pool) = row.get::<_, Option<String>>("pool_address") {
                info!(target: "process_tokens_from_launchpad", "Found pool address: {}", pool);
            } else {
                info!(target: "process_tokens_from_launchpad", "No pool address found for the given token");
                return None;
            }

            let token_0_address = row.get::<_, String>("token_0_address"); // can be stable, wrapped native token or launchpad token
            let token_1_address = row.get::<_, String>("token_1_address"); // can be stable, wrapped native token or launchpad token
            let chain_id: i64 = row.get("chain_id");
            let base_token_address: String; // token_0
            let launchpad_token_address: String; // token_1
            if token_0_address.to_lowercase() == get_stable_coin_address(&chain_id.to_string()).to_lowercase() || 
                token_0_address.to_lowercase() == get_wrapped_token_address(&chain_id.to_string()).to_lowercase() {
                base_token_address = token_0_address;
                launchpad_token_address = token_1_address.clone();
            } else {
                base_token_address = token_1_address.clone();
                launchpad_token_address = token_0_address;
            }
            info!(target: "process_tokens_from_launchpad", "base_token_address: {}", base_token_address);
            info!(target: "process_tokens_from_launchpad", "launchpad_token_address: {}", launchpad_token_address);

            let base_token_decimals = get_token_decimals(&chain_id.to_string(), &base_token_address).await;
            let launchpad_token_decimals = get_token_decimals(&chain_id.to_string(), &launchpad_token_address).await;

            if launchpad_token == &base_token_address {
                info!(target: "process_tokens_from_launchpad", "Launchpad token is the base token, returning None to get price from RPC");
                let base_token_usd_price = get_usd_value(Some(&base_token_address), &chain_id.to_string()).await; // we should get this as it can be a stable coin or wrapped native token
                info!(target: "process_tokens_from_launchpad", "base_token_usd_price: {}", base_token_usd_price);
                return Some((base_token_usd_price, base_token_decimals));
            }

            if let Some(sqrt_price) = row.get::<_, Option<String>>("sqrt_price") {
                token_out_sqrt_price = sqrt_price.parse::<f64>().unwrap_or(0.0);
            } else {
                let token_out_sqrt_price_str = row.get::<_, String>("initial_sqrt_price");
                token_out_sqrt_price = token_out_sqrt_price_str.parse::<f64>().unwrap_or(0.0);
            }
            
            let pool_address: String = row.get("pool_address");

            info!(target: "process_tokens_from_launchpad", "base_token_decimals: {}", base_token_decimals);
            info!(target: "process_tokens_from_launchpad", "launchpad_token_decimals: {}", launchpad_token_decimals);

            let base_token_usd_price = get_usd_value(Some(&base_token_address), &chain_id.to_string()).await; // we should get this as it can be a stable coin or wrapped native token
            info!(target: "process_tokens_from_launchpad", "base_token_usd_price: {}", base_token_usd_price);

            let (decimal_adjustment, is_launchpad_token1) = if launchpad_token_address == token_1_address {
                (10f64.powi((base_token_decimals - launchpad_token_decimals) as i32), true)
            } else {
                (10f64.powi((launchpad_token_decimals - base_token_decimals) as i32), false)
            };
            info!(target: "process_tokens_from_launchpad", "decimal_adjustment: {}", decimal_adjustment);

            let price_ratio = if chain_id != SOLANA_CHAIN_ID.parse::<i64>().unwrap() {
                (((token_out_sqrt_price as f64).powi(2)) as f64 / (2.0_f64.powf(192.0)))
            } else {
                (((token_out_sqrt_price as f64).powi(2)) as f64 / (2.0_f64.powf(128.0)))
            };
            info!(target: "process_tokens_from_launchpad", "price_ratio: {}", price_ratio);

            let launchpad_token_usd_price = if is_launchpad_token1 {
                base_token_usd_price * price_ratio
            } else {
                base_token_usd_price / price_ratio
            };
            info!(target: "process_tokens_from_launchpad", "launchpad_token_usd_price: {}", launchpad_token_usd_price);

            return Some((launchpad_token_usd_price, launchpad_token_decimals));
        }
        Err(_) => {
            info!(target: "process_tokens_from_launchpad", "No launchpad swap or pool found for the given token address");
            return None;
        }
    }
}

fn structure_result(
    response_fee_data: &Option<TransactionCost>,
    native_usd: f64,
    src_denominator: f64,
    dst_denominator: f64,
    cross_chain: bool,
) -> ResultCosts {
    if native_usd > 0.0 {
        let source_cost: AmountTypes = AmountTypes {
            value: Some(
                ((&response_fee_data
                    .clone()
                    .unwrap()
                    .source_cost
                    .parse::<f64>()
                    .unwrap()
                    * native_usd)
                    / src_denominator)
                    .to_string(),
            ),
            value_type: Some(String::from("USD")),
        };
        println!("{:?}", response_fee_data);
        let destination_cost: AmountTypes = AmountTypes {
            value: Some(
                ((&response_fee_data
                    .clone()
                    .unwrap()
                    .destination_cost
                    .parse::<f64>()
                    .unwrap()
                    * native_usd)
                    / dst_denominator)
                    .to_string(),
            ),
            value_type: Some(String::from("USD")),
        };
println!("destination_cost = {:?}", destination_cost);
        println!("native_value = {:?}", native_usd);
        println!("calculated destination cost = {:?}", (&response_fee_data.clone().unwrap().destination_cost.parse::<f64>().unwrap() * native_usd)/dst_denominator);
        println!("dst denominator = {:?}", dst_denominator);

        let inclusive_layer_fee: AmountTypes = AmountTypes {
            value: Some(response_fee_data.clone().unwrap().inclusive_layer_fee),
            value_type: Some(String::from("INPUT_TOKEN")),
        };

        let gas_api_response = response_fee_data.clone().unwrap();

        let flat_fees;
        let solver_fees;
        if cross_chain {
            flat_fees = AmountTypes {
                value: Some(
                    ((&response_fee_data
                        .clone()
                        .unwrap()
                        .provider_fee
                        .flat_fee
                        .unwrap()
                        .parse::<f64>()
                        .unwrap()
                        * native_usd)
                        / src_denominator)
                        .to_string(),
                ),
                value_type: Some(String::from("USD")),
            };
            solver_fees = AmountTypes {
                value: Some(String::from("1")),
                value_type: Some(String::from("USD")),
            };
        } else {
            flat_fees = AmountTypes {
                value: gas_api_response.provider_fee.flat_fee,
                value_type: Some(String::from("NATIVE")),
            };
            solver_fees = AmountTypes {
                value: None,
                value_type: None,
            };
        }

        let provider_fee: ThirdPartyFeeResult = ThirdPartyFeeResult {
            provider: gas_api_response.provider_fee.provider,
            flat_fee: flat_fees,
            variable_fee: AmountTypes {
                value: gas_api_response.provider_fee.variable_fee,
                value_type: Some(String::from("INPUT_TOKEN")),
            },
            solver_fee: solver_fees,
        };

        let result: ResultCosts = ResultCosts {
            source_cost,
            destination_cost,
            provider_fee,
            inclusive_layer_fee,
        };
        result
    } else {
        let source_cost: AmountTypes = AmountTypes {
            value: Some(response_fee_data.clone().unwrap().source_cost),
            value_type: Some(String::from("NATIVE")),
        };
        let destination_cost: AmountTypes = AmountTypes {
            value: Some(response_fee_data.clone().unwrap().destination_cost),
            value_type: Some(String::from("NATIVE")),
        };

        let inclusive_layer_fee: AmountTypes = AmountTypes {
            value: Some(response_fee_data.clone().unwrap().inclusive_layer_fee),
            value_type: Some(String::from("INPUT_TOKEN")),
        };

        let gas_api_response = response_fee_data.clone().unwrap();

        let flat_fees;
        if cross_chain {
            flat_fees = AmountTypes {
                value: response_fee_data.clone().unwrap().provider_fee.flat_fee,
                value_type: Some(String::from("NATIVE")),
            };
        } else {
            flat_fees = AmountTypes {
                value: gas_api_response.provider_fee.flat_fee,
                value_type: Some(String::from("NATIVE")),
            }
        }

        let provider_fee: ThirdPartyFeeResult = ThirdPartyFeeResult {
            provider: gas_api_response.provider_fee.provider,
            flat_fee: flat_fees,
            variable_fee: AmountTypes {
                value: gas_api_response.provider_fee.variable_fee,
                value_type: Some(String::from("INPUT_TOKEN")),
            },
            solver_fee: AmountTypes {
                value: Some(String::from("1")),
                value_type: Some(String::from("USD")),
            },
        };

        let result: ResultCosts = ResultCosts {
            source_cost,
            destination_cost,
            provider_fee,
            inclusive_layer_fee,
        };
        return result;
    }
}

async fn get_jup_quote(
    _from_chain: &str,
    from_token: &str,
    to_token: &str,
    from_amount: &str,
) -> Result<JupQuote, reqwest::Error> {
    let request_client = reqwest::Client::new();

    let response = request_client
        .get("https://api.jup.ag/swap/v1/quote")
        .header("accept", "application/json")
        .query(&[
            ("inputMint", from_token),
            ("outputMint", to_token),
            ("amount", from_amount),
        ])
        .send()
        .await;

    match response {
        Ok(res) => {
            let amount_out: JupQuote = res.json().await?;
            Ok(amount_out)
        }
        Err(e) => Err(e),
    }
}

async fn get_1_inch_quote(
    from_chain: &str,
    from_token: &str,
    to_token: &str,
    from_amount: &str,
) -> Result<OneInchQuote, reqwest::Error> {
    let request_client = reqwest::Client::new();
    let api_key = env_var("ONEINCH_API_KEY").expect("ONEINCH_API_KEY key not found in environment");

    let response = request_client
        .get(&format!(
            "https://api.1inch.dev/swap/v6.0/{}/quote",
            from_chain
        ))
        .header("accept", "application/json")
        .header("Authorization", &format!("Bearer {api_key}"))
        .query(&[
            ("src", from_token),
            ("dst", to_token),
            ("amount", from_amount),
        ])
        .send()
        .await;

    match response {
        Ok(res) => {
            let amount_out: OneInchQuote = res.json().await?;
            Ok(amount_out)
        }
        Err(e) => Err(e),
    }
}