use std::{collections::HashMap, sync::Arc, time::{SystemTime, UNIX_EPOCH}};
use log::error;
use actix_web::{HttpResponse, web::{Data, Json}};
use rust_decimal::Decimal;
use serde_json::json;
use tokio_postgres::Client;

use crate::{structs::{OHLCPricesRequest, OHLCPricesResponse, PricesRequest, PricesResponse}, util::unix_to_system_time};

pub async fn handle_get_token_prices(
    token_prices: Json<PricesRequest>,
    db_client: Data<Arc<Client>>
) -> HttpResponse {
    let pool_address = token_prices.pool_address.clone();
    let chain_id = token_prices.chain_id.clone();
    if pool_address.is_empty() || chain_id.is_empty() || pool_address.len() != chain_id.len() {
        return HttpResponse::BadRequest().json(json!({ "error": "Pool address and chain ID are required" }));
    }

    let query = "SELECT DISTINCT ON (pool_address) pool_address, price FROM ammswap WHERE pool_address = ANY($1) AND chain_id = ANY($2) ORDER BY pool_address, timestamp DESC";
    let response = db_client.query(query, &[&pool_address, &chain_id]).await;
    match response {
        Ok(rows) => {
            if let Some(_row) = rows.first() {
                let mut token_prices = HashMap::<String, Decimal>::new();
                for row in rows {
                    if let Some(price) = row.get::<_, Option<Decimal>>("price") {
                        let pool_address: String = row.get("pool_address");
                        token_prices.insert(pool_address.clone(), price);
                    }
                }
                let prices: PricesResponse = PricesResponse {
                    prices: token_prices
                };
                return HttpResponse::Ok().json(prices);
            }
            HttpResponse::NotFound().json(json!({ "error": "Price not found" }))
        }
        Err(e) => {
            error!("Database query error: {}", e);
            HttpResponse::InternalServerError().json(json!({ "error": "Internal server error" }))
        }
    }
}

pub async fn handle_ohlc_token_prices(
    token_prices: Json<OHLCPricesRequest>,
    db_client: Data<Arc<Client>>
) -> HttpResponse {
    let from_timestamp: SystemTime = match token_prices.from_timestamp {
        Some(ts) => unix_to_system_time(ts), 
        None => unix_to_system_time(chrono::Utc::now().timestamp() - 86400), // Default to 24 hours ago if not provided
    };
    let to_timestamp: SystemTime = match token_prices.to_timestamp {
        Some(ts) => unix_to_system_time(ts),
        None => unix_to_system_time(chrono::Utc::now().timestamp()), // Default to now if not provided
    };
    if from_timestamp >= to_timestamp {
        return HttpResponse::BadRequest().json(json!({ "error": "Invalid timestamp range" }));
    }
    let token_address = token_prices.token_address.clone();
    let chain_id = token_prices.chain_id.clone();
    let interval = token_prices.interval.clone().unwrap_or("1h".to_string());

    let query = "SELECT timestamp_bucket, open_price, high_price, low_price, close_price, token_address, pool_address FROM ohlc_price_tables WHERE token_address = $1 AND chain_id = $2 AND timestamp_bucket >= $3 AND timestamp_bucket < $4 AND interval = $5 ORDER BY timestamp_bucket DESC";

    let response = db_client.query(query, &[&token_address, &chain_id, &from_timestamp, &to_timestamp, &interval]).await;
    match response {
        Ok(rows) => {
            if !rows.is_empty() {
                let mut ohlc_prices = Vec::new();
                for row in rows {
                    let open_price = row.get::<_, Decimal>("open_price");
                    let high_price = row.get::<_, Decimal>("high_price");
                    let low_price = row.get::<_, Decimal>("low_price");
                    let close_price = row.get::<_, Decimal>("close_price");
                    let ohlc = HashMap::from([
                        ("open".to_string(), open_price),
                        ("high".to_string(), high_price),
                        ("low".to_string(), low_price),
                        ("close".to_string(), close_price),
                    ]);

                    let timestamp = row.get::<_, SystemTime>("timestamp_bucket");
                    let unix_timestamp = timestamp.duration_since(UNIX_EPOCH).unwrap().as_secs() as i64;
                    ohlc_prices.push((unix_timestamp, ohlc));
                }
                let response: OHLCPricesResponse = OHLCPricesResponse {
                    prices: ohlc_prices,
                    chain_id: chain_id.clone(),
                    token_address: token_address.clone(),
                };
                return HttpResponse::Ok().json(response);
            }
            HttpResponse::NotFound().json(json!({ "error": "Historical prices not found" }))
        }
        Err(e) => {
            error!("Database query error: {}", e);
            HttpResponse::InternalServerError().json(json!({ "error": "Internal server error" }))
        }
    }
}   