use alloy::{primitives::Address, sol};
use alloy::providers::ProviderBuilder;
use anyhow::Context;
use chrono::DateTime;
use log::info;
use solana_client::nonblocking::rpc_client::RpcClient;
use solana_sdk::program_pack::Pack;
use solana_sdk::pubkey::Pubkey;
use spl_token::state::Mint;
use std::ffi::OsStr;
use std::str::FromStr;
use std::time::SystemTime;

use crate::constants::SOLANA_CHAIN_ID;
use crate::structs::{JupPriceResponse, OneInchPriceResponse};

sol! {
    #[allow(missing_docs)]
    #[sol(rpc)]
    interface IERC20 {
        function decimals() external view returns (uint8);
    }
}

pub fn env_var(key: impl AsRef<OsStr>) -> anyhow::Result<String> {
    env_var_parse::<String>(key)
}

pub fn env_var_parse<T>(key: impl AsRef<OsStr>) -> anyhow::Result<T>
where
    T: FromStr,
    anyhow::Error: From<<T as FromStr>::Err>,
{
    let value =
        std::env::var(key.as_ref()).context(format!("{}", key.as_ref().to_string_lossy()))?;
    value
        .parse::<T>()
        .map_err(anyhow::Error::from)
        .context(format!("{}", key.as_ref().to_string_lossy()))
}

pub fn get_wrapped_token_address(chain_id: &str) -> String {
    match chain_id {
        "1" => String::from("0xC02aaA39b223FE8D0A0e5C4F27eAD9083C756Cc2"),
        "42161" => String::from("0x82aF49447D8a07e3bd95BD0d56f35241523fBab1"),
        "10" => String::from("0x4200000000000000000000000000000000000006"),
        "8453" => String::from("0x4200000000000000000000000000000000000006"),
        "137" => String::from("0x0d500B1d8E8eF31E21C99d1Db9A6444d3ADf1270"),
        SOLANA_CHAIN_ID => String::from("So11111111111111111111111111111111111111112"),
        &_ => String::new(),
    }
}

pub async fn get_usd_value(token_address: Option<&str>, chain_id: &str) -> f64 {
    let request_client = reqwest::Client::new();
    let query_token_address = match token_address {
        Some(address) => String::from(address),
        None => get_wrapped_token_address(chain_id), // giving None for native tokens and fetching for wrapped token
    };
    if query_token_address.len() == 0 {
        return 0.0;
    }

    let response;
    if chain_id == SOLANA_CHAIN_ID {
        // get price from jupiter api
        let url = format!("https://api.jup.ag/price/v2?ids={}", query_token_address);
        response = match request_client.get(&url).send().await {
            Ok(data) => {
                let result = match data.json::<JupPriceResponse>().await {
                    Ok(price_data) => {
                        let usd_price = price_data
                            .data
                            .get(&query_token_address)
                            .unwrap()
                            .price
                            .clone()
                            .parse::<f64>()
                            .unwrap();
                        usd_price
                    }
                    Err(_e) => 0.0,
                };
                result
            }
            Err(_e) => 0.0,
        };
    } else {
        // get price from 1inch api
        let url = format!(
            "https://api.1inch.dev/price/v1.1/{}/{}?currency=USD",
            chain_id, query_token_address
        );

        let api_key = env_var("ONEINCH_API_KEY").expect("ONEINCH_API_KEY key not found in environment");
        response = match request_client
            .get(&url)
            .header("Authorization", format!("Bearer {api_key}"))
            .send()
            .await
        {
            Ok(data) => {
                let result = match data.json::<OneInchPriceResponse>().await {
                    Ok(price_data) => {
                        let usd_price = match price_data
                            .prices
                            .get(&query_token_address.to_lowercase())
                            .cloned()
                        {
                            Some(price) => price.parse::<f64>().unwrap(),
                            None => 0.0,
                        };
                        usd_price
                    }
                    Err(_e) => 0.0,
                };
                result
            }
            Err(_e) => 0.0,
        };
    }
    info!("usd function response {:?}", response);
    response
}

pub async fn calculate_price_impact(
    amount_in: &f64,
    amount_out: &f64,
    src_chain_id: &str,
    destination_chain_id: &str,
    token_in_address: &str,
    token_out_address: &str,
) -> f64 {
    let token_in_usd_value = get_usd_value(Some(token_in_address), &src_chain_id).await;
    let token_out_usd_value = get_usd_value(Some(token_out_address), &destination_chain_id).await;
    info!("token_in_usd_value {:?}", token_in_usd_value);
    info!("token_out_usd_value {:?}", token_out_usd_value);

    let token_in_decimals = get_token_decimals(src_chain_id, token_in_address).await;
    let token_out_decimals = get_token_decimals(destination_chain_id, token_out_address).await;
    info!("token_in_decimals {:?}", token_in_decimals);
    info!("token_out_decimals {:?}", token_out_decimals);

    let formatted_amount_in = amount_in / (10.0_f64.powf(token_in_decimals));
    let formatted_amount_out = amount_out / (10.0_f64.powf(token_out_decimals));

    info!("formatted_amount_in {:?}", formatted_amount_in);
    info!("formatted_amount_out {:?}", formatted_amount_out);

    let amount_in_usd = formatted_amount_in * token_in_usd_value;
    let amount_out_usd = formatted_amount_out * token_out_usd_value;

    info!("amount_in_usd {:?}", amount_in_usd);
    info!("amount_out_usd {:?}", amount_out_usd);

    let price_impact = (amount_in_usd - amount_out_usd) / amount_in_usd;
    return price_impact;
}

pub fn get_rpc_url(chain_id: &str) -> String {
    match chain_id {
        "1" => String::from("https://dawn-radial-flower.quiknode.pro/e74a7637af460c3bb348ebdc7cd59f4374e4ece7"),
        "42161" => String::from("https://smart-young-dust.arbitrum-mainnet.quiknode.pro/81fb7bc3cb557c5f57ce646e35de91ed8dae5557"),
        "10" => String::from("https://blue-neat-mountain.optimism.quiknode.pro/794dff6716a069d1d4d0c6b5bf6d4456f3848618"),
        "8453" => String::from("https://light-quaint-sound.base-mainnet.quiknode.pro/1be86f76089a93cdec8402a55cf83d30d1091cef"),
        "137" => String::from("https://prettiest-few-pine.matic.quiknode.pro/cb499925dd6d5b0649febdd489afce406924d074"),
        SOLANA_CHAIN_ID => String::from("https://mainnet.helius-rpc.com/?api-key=d4d3c545-bd81-405c-9e51-3f600e9c25ad"),
        &_ => String::new()
    }
}

pub async fn get_token_decimals(chain_id: &str, token_address: &str) -> f64 {
    let rpc_url = get_rpc_url(chain_id);
    if chain_id == SOLANA_CHAIN_ID {
        let solana_client = RpcClient::new(rpc_url);
        let mint_pubkey = token_address.parse::<Pubkey>().unwrap();
        let account_data = solana_client.get_account_data(&mint_pubkey).await.unwrap();
        let mint = Mint::unpack(&account_data).unwrap();
        let decimals = mint.decimals;
        decimals as f64
    } else {
        let provider = ProviderBuilder::new().on_http(rpc_url.parse().unwrap());
        let token = Address::from_str(token_address).unwrap();
        // Create an instance of IERC20 for the token address
        let erc20 = IERC20::new(token, provider);

        // Call the decimals function
        let decimals = erc20.decimals().call().await.unwrap()._0;
        decimals as f64
    }
}

pub fn get_stable_coin_address(chain_id: &str) -> &str {
    match chain_id {
        "1" => "0xA0b86991c6218b36c1d19D4a2e9Eb0cE3606eB48",
        "42161" => "0xaf88d065e77c8cC2239327C5EDb3A432268e5831",
        "10" => "******************************************",
        "137" => "******************************************",
        "8453" => "******************************************",
        SOLANA_CHAIN_ID => "EPjFWdd5AufqSSqeM2qN1xzybapC8G4wEGGkZwyTDt1v",
        &_ => "",
    }
}

pub fn get_chain_id(chain_name: &str) -> Option<u64> {
    match chain_name {
        "arbitrum" => Some(42161),
        "optimism" => Some(10),
        "polygon" => Some(137),
        "ethereum" => Some(1),
        "base" => Some(8453),
        "solana" => Some(SOLANA_CHAIN_ID.parse().unwrap()),
        _ => None,
    }
}

pub fn unix_to_system_time(timestamp_secs: i64) -> SystemTime {
    let dt_utc =
        DateTime::from_timestamp(timestamp_secs, 0).expect("timestamp out of range");
    dt_utc.into()
}