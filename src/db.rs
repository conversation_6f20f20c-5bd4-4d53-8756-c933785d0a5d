use tokio_postgres::{<PERSON><PERSON><PERSON>, Client};
use openssl::ssl::{SslConnector, SslMethod};
use postgres_openssl::MakeTlsConnector;
use log::error;
use crate::util::env_var;

pub async fn connect_to_db() -> anyhow::Result<Client> {
    let db_connection_string = env_var("DB_CONNECTION_STRING").expect("DB_CONNECTION_STRING must be set in environment");
    let mut builder = SslConnector::builder(SslMethod::tls()).unwrap();
    builder.set_verify(openssl::ssl::SslVerifyMode::NONE); //TODO: use the verification in production
    let connector = MakeTlsConnector::new(builder.build());
    let (client, connection) = tokio_postgres::connect(&db_connection_string, connector).await.unwrap();

    tokio::spawn(async move {
        if let Err(e) = connection.await {
            error!("DB Connection error {:?}", e);
        }
    });

    Ok(client)
}