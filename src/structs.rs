use std::collections::HashMap;

use chrono::NaiveDateTime;
use rust_decimal::Decimal;
use serde::{Deserialize, Serialize};
use serde_json::Value;
use time::OffsetDateTime;

use crate::feed::Feed;

// Add these new structs for handling quote requests and responses
#[derive(Debug, Deserialize, Serialize)]
pub struct QuoteRequest {
    pub from_chain: String,
    pub to_chain: String,
    pub from_token: String,
    pub to_token: String,
    pub from_amount: String,
    pub from_address: String,
    pub slippage: Option<String>,
    // pub provider: Option<String>, // launchpad
    // pub pool_address: Option<String>, // for launchpad
}

#[derive(Debug, Serialize, Deserialize)]
pub struct QuoteResponse {
    pub from_chain: String,
    pub to_chain: String,
    pub from_token: String,
    pub to_token: String,
    pub from_amount: String,
    pub to_amount: Option<String>,
    #[serde(with = "time::serde::timestamp")]
    pub timestamp: OffsetDateTime,
}

#[derive(Debug, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct TokenData {
    pub categories: Vec<String>,
    pub cc_id: Option<i64>,
    #[serde(with = "firestore::serialize_as_null")]
    pub cmc_id: Option<Value>,
    pub coingecko_id: Option<String>,
    pub chains: Vec<Chain>,
    pub description: Option<String>,
    pub full_name: Option<String>,
    pub id: Option<String>,
    pub image_url: Option<String>,
    pub is_default: Option<bool>,
    pub is_stable: Option<bool>,
    pub is_tradable: Option<bool>,
    pub is_view_only: Option<bool>,
    pub launch_date: Option<String>,
    pub links: Option<Links>,
    pub market_data: Option<MarketData>,
    pub rate_data: Option<RateData>,
    pub ticker: Option<String>,
    pub updated_at: Option<String>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct Chain {
    pub address: String,
    pub decimals: u8,
    pub id: String,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct Links {
    pub twitter: Option<String>,
    pub website: Option<String>,
}

#[derive(Debug, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct MarketData {
    pub circulating_supply: f64,
    pub fdv: f64,
    pub liquidity_depth: String,
    pub token_rank: i64,
    pub total_supply: f64,
    pub trading_volume_24h: f64,
}

#[derive(Debug, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct RateData {
    #[serde(rename = "1d")]
    pub one_d: Vec<RatePoint>,
    pub percent_change: f64,
    pub rate: f64,
    pub updated_at: String,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct RatePoint {
    pub rate: f64,
    pub timestamp: i64,
}

#[derive(Serialize)]
pub struct FeedsResponse {
    pub feeds: HashMap<String, Feed>,
}

#[derive(Debug, Serialize, Deserialize)]
pub struct OneInchQuote {
    #[serde(rename = "dstAmount")]
    pub amount: String,
}

#[derive(Debug, Serialize, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct JupQuote {
    pub out_amount: String,
    pub price_impact_pct: String,
}


#[derive(Serialize, Deserialize, Debug, Clone)]
pub struct TransactionCost {
    pub source_cost: String,      // native
    pub destination_cost: String, // native
    pub provider_fee: ThirdPartyFees,
    pub inclusive_layer_fee: String,
}

#[derive(Serialize, Deserialize, Debug, Clone)]
pub struct ThirdPartyFees {
    pub provider: String,
    pub flat_fee: Option<String>,     // native
    pub variable_fee: Option<String>, // in from_token
    pub solver_fee: Option<String>,   // $1
}

#[derive(Serialize, Deserialize, Debug, Clone)]
pub struct AmountTypes {
    pub value: Option<String>,
    pub value_type: Option<String>,
}

#[derive(Serialize, Deserialize, Debug, Clone)]
pub struct ThirdPartyFeeResult {
    pub provider: String,
    pub flat_fee: AmountTypes,     // native
    pub variable_fee: AmountTypes, // in from_token
    pub solver_fee: AmountTypes,   // $1
}

#[derive(Serialize, Deserialize, Debug, Clone)]
pub struct ResultCosts {
    pub source_cost: AmountTypes,
    pub destination_cost: AmountTypes,
    pub provider_fee: ThirdPartyFeeResult,
    pub inclusive_layer_fee: AmountTypes,
}

#[derive(Debug, serde::Deserialize, serde::Serialize)]
pub struct PriceUsdRequest {
    pub token_address: String,
    pub chain_id: String,
    pub amount: String,
}

#[derive(Debug, Deserialize)]
pub struct OneInchPriceResponse {
    #[serde(flatten)]
    pub prices: HashMap<String, String>,
}

#[allow(dead_code)]
#[derive(Debug, Deserialize)]
pub struct TokenPrice {
    pub id: String,
    #[serde(rename = "type")]
    pub price_type: String,
    pub price: String,
}

#[allow(dead_code)]
#[derive(Debug, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct JupPriceResponse {
    pub data: HashMap<String, TokenPrice>,
    pub time_taken: f64,
}

#[allow(dead_code)]
#[derive(Debug, Deserialize, Clone)]
pub struct Token {
    pub address: String,
    pub name: String,
    pub symbol: String,
    pub decimals: u8,
    #[serde(rename = "logoURI")]
    logo_uri: Option<String>,
    pub tags: Vec<String>,
    daily_volume: Option<f64>,
    created_at: String,
    freeze_authority: Option<String>,
    mint_authority: Option<String>,
    permanent_delegate: Option<String>,
    minted_at: Option<String>,
    #[serde(skip)]
    extensions: serde_json::Value,
}

#[allow(dead_code)]
#[derive(Debug, Serialize)]
#[serde(rename_all = "camelCase")]
pub struct CreateOrderRequest {
    pub src_chain_id: String,
    #[serde(rename = "srcChainTokenIn")]
    pub src_token: String,
    #[serde(rename = "srcChainTokenInAmount")]
    pub amount: String,
    pub dst_chain_id: String,
    #[serde(rename = "dstChainTokenOut")]
    pub dst_token: String,
    #[serde(rename = "dstChainTokenOutRecipient")]
    pub receiver: String,
    #[serde(rename = "dstChainTokenOutAmount")]
    pub dst_amount: String,
}

#[allow(dead_code)]
#[derive(Debug, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct CreateOrderResponse {
    pub estimation: Estimation,
    pub tx: Option<TransactionInfo>,
    pub order: OrderInfo,
    pub fix_fee: String,
    pub user_points: f64,
    pub integrator_points: f64,
}

#[allow(dead_code)]
#[derive(Debug, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct Estimation {
    pub src_chain_token_in: TokenInfo,
    pub dst_chain_token_out: TokenInfo,
    pub recommended_slippage: f64,
}

#[allow(dead_code)]
#[derive(Debug, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct TokenInfo {
    pub address: String,
    pub chain_id: u64,
    pub decimals: u8,
    pub name: String,
    pub symbol: String,
    pub amount: String,
    pub approximate_usd_value: f64,
    pub approximate_operating_expense: Option<String>,
}

#[allow(dead_code)]
#[derive(Debug, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct TransactionInfo {
    pub allowance_target: String,
}

#[allow(dead_code)]
#[derive(Debug, Deserialize)]
#[serde(rename_all = "camelCase")]
pub struct OrderInfo {
    pub approximate_fulfillment_delay: u64,
}

#[derive(Debug, Serialize)]
pub struct PricesResponse {
    pub prices: HashMap<String, Decimal>,
}

#[derive(Debug, Deserialize)]
pub struct PricesRequest {
    pub from_timestamp: Option<u64>,
    pub to_timestamp: Option<u64>,
    pub chain_id: Vec<i64>,
    pub pool_address: Vec<String>
}

#[derive(Debug, Deserialize)]
pub struct OHLCPricesRequest {
    pub from_timestamp: Option<i64>,
    pub to_timestamp: Option<i64>,
    pub interval: Option<String>,
    pub chain_id: i64,
    pub token_address: String
}

#[derive(Debug, Serialize)]
pub struct OHLCPricesResponse {
    pub prices: Vec<(i64, HashMap<String, Decimal>)>,
    pub chain_id: i64,
    pub token_address: String
}
