use std::{
    collections::{HashMap, HashSet},
    sync::Arc,
    time::Duration,
};
use time::OffsetDateTime;
use tokio::sync::broadcast;

use crate::{errors, feed::Feed, feed_updater::FeedUpdater, structs::{JupPriceResponse, Token}};

pub struct Jup {
    update_frequency_millis: u64,
    tracked_tokens: HashSet<String>,
    tx: Arc<broadcast::Sender<Feed>>,
    supported_chains: HashSet<String>,
}

impl Jup {
    pub fn new(
        tracked_tokens: HashSet<String>,
        update_frequency_millis: u64,
        tx: Arc<broadcast::Sender<Feed>>,
        supported_chains: HashSet<String>,
    ) -> Self {
        Self {
            update_frequency_millis,
            tracked_tokens,
            tx,
            supported_chains,
        }
    }

    pub async fn get_token_prices(
        &self,
        tokens: &[&str],
    ) -> Result<HashMap<String, f64>, errors::Error> {
        let mut prices = HashMap::new();
        const CHUNK_SIZE: usize = 45;
        const MAX_RETRIES: usize = 3;

        for chunk in tokens.chunks(CHUNK_SIZE) {
            let mut retries = 0;
            loop {
                let url = format!("https://api.jup.ag/price/v2?ids={}", chunk.join(","));
                match reqwest::get(&url).await {
                    Ok(response) => match response.json::<JupPriceResponse>().await {
                        Ok(price_response) => {
                            for token in chunk {
                                if let Some(price_data) = price_response.data.get(*token) {
                                    if let Ok(price) = price_data.price.parse::<f64>() {
                                        if price != 0.0 {
                                            prices.insert(token.to_string(), price);
                                        }
                                    }
                                }
                            }
                            break;
                        }
                        Err(_) => {
                            retries += 1;
                            if retries >= MAX_RETRIES {
                                break;
                            }
                            tokio::time::sleep(Duration::from_secs(3)).await;
                        }
                    },
                    Err(_) => {
                        retries += 1;
                        if retries >= MAX_RETRIES {
                            break;
                        }
                        tokio::time::sleep(Duration::from_secs(3)).await;
                    }
                }
            }
        }

        if prices.is_empty() {
            return Err(errors::Error::Jup("No valid prices received".to_string()));
        }

        Ok(prices)
    }

    /// Gets all verified tokens from Jupiter
    pub async fn get_verified_tokens(&self) -> Result<Vec<Token>, errors::Error> {
        let url = "https://tokens.jup.ag/tokens?tags=verified";

        let response = reqwest::get(url)
            .await
            .map_err(|e| errors::Error::Jup(e.to_string()))?;

        Ok(response
            .json()
            .await
            .map_err(|e| errors::Error::Jup(e.to_string()))?)
    }
}

impl FeedUpdater for Jup {
    fn get_frequency(&self) -> u64 {
        self.update_frequency_millis
    }

    fn update(
        &self,
        _chain_name: String,
    ) -> impl std::future::Future<Output = Result<Feed, errors::Error>> + Send {
        async move {
            let tokens = self.get_verified_tokens().await?;
            let prices = self
                .get_token_prices(
                    &tokens
                        .iter()
                        .filter(|t| self.tracked_tokens.contains(&t.address))
                        .map(|t| t.address.as_str())
                        .collect::<Vec<_>>()
                        .as_slice(),
                )
                .await?;
            Ok(Feed {
                prices,
                source: "jup".to_string(),
                timestamp: OffsetDateTime::now_utc().unix_timestamp() as u64,
                supported_chains: self.supported_chains.clone(),
            })
        }
    }
}

#[cfg(test)]
mod tests {
    // ... keep existing mock tests ...

    mod integration {

        impl Jup {
            pub async fn get_token_by_mint(&self, mint: &str) -> Result<Token, errors::Error> {
                let url = format!("https://tokens.jup.ag/token/{}", mint);
                let response = reqwest::get(url)
                    .await
                    .map_err(|e| errors::Error::Jup(e.to_string()))?;
                let token: Token = response
                    .json()
                    .await
                    .map_err(|e| errors::Error::Jup(e.to_string()))?;
                Ok(token)
            }
        }

        use super::super::*;

        #[tokio::test]
        async fn test_real_sol_price() {
            let (tx, _) = broadcast::channel::<Feed>(100);
            let tx = Arc::new(tx);
            let jup = Jup::new(
                ["So11111111111111111111111111111111111111112".to_string()]
                    .iter()
                    .cloned()
                    .collect(),
                1000 * 60 * 60 * 24,
                tx,
                vec!["solana".into()].into_iter().collect(),
            );
            // Using SOL's mint address
            let result = jup
                .get_token_prices(&["So11111111111111111111111111111111111111112"])
                .await;
            // info!("result: {:?}", result);
            assert!(result.is_ok());

            let prices = result.unwrap();
            // SOL should have a positive price
            assert!(
                prices
                    .get("So11111111111111111111111111111111111111112")
                    .unwrap()
                    > &0.0
            );
            // SOL price should be reasonable (adjust these bounds as needed)
            assert!(
                prices
                    .get("So11111111111111111111111111111111111111112")
                    .unwrap()
                    < &1000.0
            );
        }

        #[tokio::test]
        async fn test_real_token_by_mint() {
            let (tx, _) = broadcast::channel::<Feed>(100);
            let tx = Arc::new(tx);
            let jup = Jup::new(
                ["So11111111111111111111111111111111111111112".to_string()]
                    .iter()
                    .cloned()
                    .collect(),
                1000 * 60 * 60 * 24,
                tx,
                vec!["solana".into()].into_iter().collect(),
            );
            // SOL mint address
            let mint = "So11111111111111111111111111111111111111112";
            let result = jup.get_token_by_mint(mint).await;
            assert!(result.is_ok());

            let address = result.unwrap().address;
            assert_eq!(address, mint);
        }

        #[tokio::test]
        async fn test_invalid_token_price() {
            let (tx, _) = broadcast::channel::<Feed>(100);
            let tx = Arc::new(tx);
            let jup = Jup::new(
                ["So11111111111111111111111111111111111111112".to_string()]
                    .iter()
                    .cloned()
                    .collect(),
                1000 * 60 * 60 * 24,
                tx,
                vec!["solana".into()].into_iter().collect(),
            );
            let result = jup.get_token_prices(&["INVALID_TOKEN_123"]).await;
            assert!(result.is_err());
        }

        #[tokio::test]
        async fn test_invalid_mint() {
            let (tx, _) = broadcast::channel::<Feed>(100);
            let tx = Arc::new(tx);
            let jup = Jup::new(
                ["So11111111111111111111111111111111111111112".to_string()]
                    .iter()
                    .cloned()
                    .collect(),
                1000 * 60 * 60 * 24,
                tx,
                vec!["solana".into()].into_iter().collect(),
            );
            let result = jup.get_token_by_mint("INVALID_MINT_123").await;
            assert!(result.is_err());
        }

        #[tokio::test]
        async fn test_get_verified_tokens() {
            let (tx, _) = broadcast::channel::<Feed>(100);
            let tx = Arc::new(tx);
            let jup = Jup::new(
                ["So11111111111111111111111111111111111111112".to_string()]
                    .iter()
                    .cloned()
                    .collect(),
                1000 * 60 * 60 * 24,
                tx,
                vec!["solana".into()].into_iter().collect(),
            );
            let result = jup.get_verified_tokens().await;
            assert!(result.is_ok());

            let tokens = result.unwrap();
            // assert!(!tokens.is_empty());

            // Verify that all tokens have the "verified" tag
            for token in tokens {
                assert!(token.tags.contains(&"verified".to_string()));
            }
        }

        #[tokio::test]
        async fn test_get_verified_tokens_cache() {
            let (tx, _) = broadcast::channel::<Feed>(100);
            let tx = Arc::new(tx);
            let jup = Jup::new(
                ["So11111111111111111111111111111111111111112".to_string()]
                    .iter()
                    .cloned()
                    .collect(),
                1000 * 60 * 60 * 24,
                tx,
                vec!["solana".into()].into_iter().collect(),
            );
            let result = jup.get_verified_tokens().await;
            assert!(result.is_ok());
        }

        #[tokio::test]
        async fn test_update() {
            let (tx, _) = broadcast::channel::<Feed>(100);
            let tx = Arc::new(tx);
            let jup = Jup::new(
                ["So11111111111111111111111111111111111111112".to_string()]
                    .iter()
                    .cloned()
                    .collect(),
                1000 * 60 * 60 * 24,
                tx,
                vec!["solana".into()].into_iter().collect(),
            );
            let result = jup.update("solana".to_string()).await;
            dbg!(&result);
            assert!(result.is_ok());

            let feed = result.unwrap();
            // Verify that the feed contains data
            assert!(!feed.prices.is_empty());

            // Check that each entry has a positive price
            for (_, price) in feed.prices {
                assert!(price > 0.0);
            }
        }
    }
    use super::*;

    #[tokio::test]
    async fn test_get_verified_tokens_cache_2() {
        let input = r#"[
  {
    "address": "So11111111111111111111111111111111111111112",
    "name": "Wrapped SOL",
    "symbol": "SOL",
    "decimals": 9,
    "logoURI": "https://raw.githubusercontent.com/solana-labs/token-list/main/assets/mainnet/So11111111111111111111111111111111111111112/logo.png",
    "tags": [
      "community",
      "verified",
      "strict"
    ],
    "daily_volume": 1709149518.49515,
    "created_at": "2024-04-26T10:56:58.893768Z",
    "freeze_authority": null,
    "mint_authority": null,
    "permanent_delegate": null,
    "minted_at": null,
    "extensions": {
      "coingeckoId": "wrapped-solana"
    }
  }
]
"#;

        let tokens: Vec<Token> = serde_json::from_str(input).unwrap();
        dbg!(tokens);
    }
}
