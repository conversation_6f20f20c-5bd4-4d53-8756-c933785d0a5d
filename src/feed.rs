use std::collections::{HashMap, HashSet};

use serde::{Deserialize, Serialize};

#[derive(<PERSON>bu<PERSON>, <PERSON><PERSON>, <PERSON><PERSON>ult, Serialize, Deserialize)]
pub struct Feed {
    pub prices: HashMap<String, f64>,
    pub source: String,
    pub timestamp: u64,
    pub supported_chains: HashSet<String>,
}

impl Feed {
    pub fn new() -> Self {
        Feed {
            prices: HashMap::new(),
            source: "undefined".into(),
            timestamp: 0,
            supported_chains: HashSet::new(),
        }
    }

    pub fn add_supported_chain(&mut self, chain: String) {
        self.supported_chains.insert(chain);
    }

    pub fn supports_chain(&self, chain: &str) -> bool {
        self.supported_chains.contains(chain)
    }
}
