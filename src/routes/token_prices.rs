use actix_web::{web::{J<PERSON>, <PERSON>}, HttpResponse, get, post};
use serde_json::json;
use tokio_postgres::Client;
use std::{sync::{Arc, Mutex}, collections::HashMap};
use crate::{
    feed::Feed, handlers::handle_token_prices::{handle_get_token_prices, handle_ohlc_token_prices}, structs::{FeedsResponse, OHLCPricesRequest, PriceUsdRequest, PricesRequest}, util::{get_token_decimals, get_usd_value}
};

#[post("/price_in_usd")]
pub async fn get_price_in_usd(req: Json<PriceUsdRequest>) -> HttpResponse {
    // get token decimals first
    let decimals = get_token_decimals(&req.chain_id, &req.token_address).await;

    let amount_in = req.amount.parse::<u128>().unwrap();
    let formatted_amount_in = (amount_in / (10_u128.pow(decimals as u32))) as f64;

    // get usd value
    let token_price_usd = get_usd_value(Some(&req.token_address), &req.chain_id).await;
    let amount_usd_value = formatted_amount_in * token_price_usd;

    HttpResponse::Ok().json(json!({ "amount_usd_value": amount_usd_value }))
}

#[get("/price")]
pub async fn latest_feed(shared_feeds: Data<Arc<Mutex<HashMap<String, Feed>>>>) -> HttpResponse {
    let feeds = shared_feeds.lock().unwrap();
    let response = FeedsResponse {
        feeds: feeds.clone(),
    };
    HttpResponse::Ok().json(response)
}

#[post("/token_price")]
pub async fn get_token_price(
    token_request: Json<PricesRequest>,
    db_client: Data<Arc<Client>>,
) -> HttpResponse {
    handle_get_token_prices(token_request, db_client).await
}

#[post("/token_ohlc_price")]
pub async fn get_token_ohlc_price(
    token_request: Json<OHLCPricesRequest>,
    db_client: Data<Arc<Client>>,
) -> HttpResponse {
    handle_ohlc_token_prices(token_request, db_client).await
}