# Build stage
FROM rust:1.85-slim-bullseye as builder

# Create a new empty shell project
WORKDIR /usr/src/price-feed
COPY . .

# Install OpenSSL - required for HTTPS requests
RUN apt-get update && apt-get install -y \
    ca-certificates \
    openssl \
    pkg-config \
    libssl-dev \
    build-essential \
    && rm -rf /var/lib/apt/lists/*

# Build with release profile
RUN cargo build --release

# Runtime stage
FROM debian:bullseye-slim

# Install OpenSSL - required for HTTPS requests
RUN apt-get update && apt-get install -y \
    ca-certificates \
    openssl \
    pkg-config \
    libssl-dev \
    build-essential \
    && rm -rf /var/lib/apt/lists/*

# Copy the binary from builder
COPY --from=builder /usr/src/price-feed/target/release/price-feed /usr/local/bin/price-feed
COPY --from=builder /usr/src/price-feed/keystore.json /usr/local/bin/keystore.json

# Create a non-root user
RUN useradd -ms /bin/bash feeder

# Switch to non-root user
USER feeder

# Copy .env file if needed
# COPY .env /usr/local/bin/.env

# Set the binary as the entrypoint
ENTRYPOINT ["/usr/local/bin/price-feed"]

# Expose port 8084
EXPOSE 8084